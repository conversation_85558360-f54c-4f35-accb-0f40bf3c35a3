import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons, MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import BottomSheet, { BottomSheetScrollView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '~/components/RenderBackdrop';
import { Person } from '~/types/people_type';
import people from '~/data/people.json';
import { useEvent } from '~/providers/MapProvider';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export interface PersonProfileSheetHandle {
  present: (userId: number) => void;
  dismiss: () => void;
}

interface PersonProfileSheetProps {
  onViewStory?: (userId: number) => void;
  onChat?: (userId: number) => void;
  onAddFriend?: (userId: number) => void;
}

const PersonProfileSheet = forwardRef<PersonProfileSheetHandle, PersonProfileSheetProps>(
  ({ onViewStory, onChat, onAddFriend }, ref) => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const { colors, colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';
    const router = useRouter();
    const { userId, setUserId, setIsPersonProfileSheetOpen, isPersonProfileSheetOpen } = useEvent();
    const [person, setPerson] = useState<Person | null>(null);
    const [isFriend, setIsFriend] = useState(false);
    const insets = useSafeAreaInsets();

    // Mock data for people with stories
    const peopleWithStories = [2, 3, 7];

    const snapPoints = ['100%'];

    useEffect(() => {
      if (userId) {
        bottomSheetRef.current?.expand();
      } else {
        bottomSheetRef.current?.close();
      }
    }, [userId]);

    useEffect(() => {
      if (userId) {
        const foundPerson = people.find((p) => p.id === userId);
        if (foundPerson) {
          setPerson(foundPerson);
          // Mock friendship status - in a real app this would come from an API
          setIsFriend([2, 3, 5].includes(userId));
        }
      }
    }, [userId]);

    const handleViewStory = () => {
      setIsPersonProfileSheetOpen(false);
      bottomSheetRef.current?.close();
      if (userId && peopleWithStories.includes(userId)) {
        if (onViewStory) {
          onViewStory(userId);
        } else {
          router.push({
            pathname: '/story/view',
            params: { userId },
          });
        }
      }
    };

    const handleChat = () => {
      setIsPersonProfileSheetOpen(false);
      bottomSheetRef.current?.close();
      if (userId) {
        if (onChat) {
          onChat(userId);
        } else {
          router.push({
            pathname: '/chat',
            params: { userId, name: person?.name },
          });
        }
      }
    };

    const handleAddFriend = () => {
      if (userId) {
        if (onAddFriend) {
          onAddFriend(userId);
        } else {
          // Mock adding friend
          setIsFriend(true);
        }
      }
    };

    const formatTimeAgo = (dateString: string) => {
      if (!dateString) return 'Offline';

      const date = new Date(dateString);
      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

      if (diffInSeconds < 60) {
        return `${diffInSeconds}s ago`;
      } else if (diffInSeconds < 3600) {
        return `${Math.floor(diffInSeconds / 60)}m ago`;
      } else if (diffInSeconds < 86400) {
        return `${Math.floor(diffInSeconds / 3600)}h ago`;
      } else {
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
      }
    };

    if (!person) return null;

    const hasStory = peopleWithStories.includes(person.id);

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={userId ? 0 : -1}
        snapPoints={['100%']}
        enablePanDownToClose
        backdropComponent={RenderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        containerStyle={{
          zIndex: 100,
          elevation: 100,
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}
        topInset={insets.top}
        onClose={() => {
          setUserId(null);
          setIsPersonProfileSheetOpen(false);
        }}>
        <BottomSheetScrollView contentContainerStyle={{ paddingBottom: 30 }}>
          {/* Profile Header */}
          <View
            className="flex-row items-center justify-between border-b px-4 py-4"
            style={{ borderBottomColor: colors.grey5 }}>
            <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
              {person.name}'s Profile
            </Text>
            <TouchableOpacity
              className="p-2"
              onPress={() => {
                setUserId(null);
                setIsPersonProfileSheetOpen(false);
              }}>
              <Ionicons name="close" size={24} color={colors.foreground} />
            </TouchableOpacity>
          </View>
          <View className="relative">
            {/* Cover Image */}

            <View className="h-32 bg-violet-600/20" />

            {/* Profile Image */}
            <View className="absolute left-4 top-16 items-center">
              <View className={`rounded-full p-1 ${hasStory ? 'bg-violet-600' : 'bg-transparent'}`}>
                <Image
                  source={{ uri: person.profilePhoto }}
                  className="h-24 w-24 rounded-full border-2 border-white"
                />
              </View>
            </View>

            {/* Action Buttons */}
            <View className="mt-12 flex-row justify-end p-4">
              {hasStory && (
                <TouchableOpacity
                  className="mr-3 h-10 w-10 items-center justify-center rounded-full bg-violet-600"
                  onPress={handleViewStory}>
                  <Ionicons name="play" size={18} color="#fff" />
                </TouchableOpacity>
              )}

              <TouchableOpacity
                className="mr-3 h-10 w-10 items-center justify-center rounded-full"
                style={{ backgroundColor: colors.grey5 }}
                onPress={handleChat}>
                <Ionicons name="chatbubble-outline" size={18} color={colors.foreground} />
              </TouchableOpacity>

              {!isFriend ? (
                <TouchableOpacity
                  className="h-10 flex-row items-center justify-center rounded-full bg-violet-600 px-4"
                  onPress={handleAddFriend}>
                  <Ionicons name="person-add-outline" size={16} color="#fff" className="mr-1" />
                  <Text className="ml-1 font-medium text-white">Add Friend</Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  className="h-10 flex-row items-center justify-center rounded-full px-4"
                  style={{ backgroundColor: colors.grey5 }}>
                  <Ionicons name="checkmark" size={16} color={colors.foreground} className="mr-1" />
                  <Text style={{ color: colors.foreground }} className="ml-1 font-medium">
                    Friends
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Profile Info */}
          <View className="mt-2 px-4">
            <Text className={`font-bold text-2xl ${isDark ? 'text-white' : 'text-black'}`}>
              {person.name}, {person.age}
            </Text>

            <View className="mb-3 mt-1 flex-row items-center">
              <View
                className={`mr-2 h-2.5 w-2.5 rounded-full ${person.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}
              />
              <Text className={`${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                {person.isOnline ? 'Online' : formatTimeAgo(person.lastActive)}
              </Text>
              {person.location && (
                <Text className={`ml-2 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  • {person.location}
                </Text>
              )}
            </View>

            {person.bio && (
              <Text className={`mb-4 text-base ${isDark ? 'text-white' : 'text-black'}`}>
                {person.bio}
              </Text>
            )}

            {/* Interests */}
            {person.interests && person.interests.length > 0 && (
              <View className="mb-6">
                <Text className={`mb-2 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
                  Interests
                </Text>
                <View className="flex-row flex-wrap">
                  {person.interests.map((interest, index) => (
                    <View
                      key={index}
                      className="mb-2 mr-2 rounded-full px-3 py-1.5"
                      style={{ backgroundColor: colors.grey5 }}>
                      <Text className="font-medium text-sm" style={{ color: colors.foreground }}>
                        {interest}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {/* Up For */}
            {person.upFor && (
              <View className="mb-6">
                <Text className={`mb-2 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
                  Up For
                </Text>
                <View className="rounded-lg px-3 py-2" style={{ backgroundColor: colors.grey5 }}>
                  <Text className="text-base" style={{ color: colors.foreground }}>
                    {person.upFor}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </BottomSheetScrollView>
      </BottomSheet>
    );
  }
);

export default PersonProfileSheet;
