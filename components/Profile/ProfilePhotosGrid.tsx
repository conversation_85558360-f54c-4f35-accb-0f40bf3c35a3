import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  FlatList,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Toast } from 'toastify-react-native';

import PhotoViewModal from './PhotoViewModal';
import { useColorScheme } from '~/lib/useColorScheme';
import { FileService } from '~/services/FileService';
import { UserStore } from '~/store/store';

interface ProfilePhoto {
  id: string;
  secureUrl: string;
  publicId?: string;
}

interface ProfilePhotosGridProps {
  userId: string;
  photos: ProfilePhoto[];
  onPhotosUpdate: (photos: ProfilePhoto[]) => void;
}

const { width } = Dimensions.get('window');
const GRID_ITEM_SIZE = (width - 48) / 3; // 3 columns with padding

export default function ProfilePhotosGrid({
  userId,
  photos,
  onPhotosUpdate,
}: ProfilePhotosGridProps) {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isUploading, setIsUploading] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<ProfilePhoto | null>(null);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission required', 'Gallery permission is required to select photos');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        await uploadImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const takePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission required', 'Camera permission is required to take photos');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        await uploadImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const uploadImage = async (uri: string) => {
    try {
      setIsUploading(true);

      // Create file object from URI
      const filename = uri.split('/').pop() || 'image.jpg';
      const fileType = filename.split('.').pop() || 'jpg';

      const file = {
        uri,
        type: `image/${fileType}`,
        name: filename,
      } as any;

      const response = await FileService.uploadManyImages([file], userId, 'PROFILE_UPLOAD');

      // Update user store with new data
      (UserStore.getState() as { setUser: (data: any) => void }).setUser(response.body);

      // Update local photos state
      if (response.body.profilePicture) {
        onPhotosUpdate(response.body.profilePicture);
      }

      Toast.show({
        type: 'success',
        text1: 'Photo Added',
        text2: 'Your photo has been added to your profile.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      Toast.show({
        type: 'error',
        text1: 'Upload Failed',
        text2: 'Failed to upload photo. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const showImageOptions = () => {
    Alert.alert(
      'Add Photo',
      'Choose how you want to add a photo',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Take Photo', onPress: takePhoto },
        { text: 'Choose from Gallery', onPress: pickImage },
      ],
      { cancelable: true }
    );
  };

  const deletePhoto = (photo: ProfilePhoto) => {
    Alert.alert(
      'Delete Photo',
      'Are you sure you want to delete this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const updatedPhotos = photos.filter((p) => p.id !== photo.id);
            onPhotosUpdate(updatedPhotos);
            Toast.show({
              type: 'success',
              text1: 'Photo Deleted',
              text2: 'Photo has been removed from your profile.',
              position: 'bottom',
              theme: isDark ? 'dark' : 'light',
              backgroundColor: colors.background,
              autoHide: true,
            });
          },
        },
      ],
      { cancelable: true }
    );
  };

  const viewPhoto = (photo: ProfilePhoto) => {
    setSelectedPhoto(photo);
    setIsViewModalVisible(true);
  };

  const renderPhotoItem = ({ item }: { item: ProfilePhoto }) => (
    <TouchableOpacity
      onPress={() => viewPhoto(item)}
      onLongPress={() => deletePhoto(item)}
      className="relative mb-2"
      style={{ width: GRID_ITEM_SIZE, height: GRID_ITEM_SIZE }}>
      <Image
        source={{ uri: item.secureUrl }}
        className="h-full w-full rounded-lg"
        resizeMode="cover"
      />
      <TouchableOpacity
        className="absolute right-1 top-1 rounded-full bg-red-500 p-1"
        onPress={() => deletePhoto(item)}>
        <Ionicons name="close" size={12} color="#fff" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderAddButton = () => (
    <TouchableOpacity
      onPress={showImageOptions}
      disabled={isUploading}
      className="items-center justify-center rounded-lg border-2 border-dashed"
      style={{
        width: GRID_ITEM_SIZE,
        height: GRID_ITEM_SIZE,
        borderColor: colors.grey,
        backgroundColor: colors.grey5,
      }}>
      {isUploading ? (
        <ActivityIndicator size="small" color={colors.primary} />
      ) : (
        <>
          <Ionicons name="add" size={32} color={colors.grey} />
          <Text className="mt-1 text-xs" style={{ color: colors.grey }}>
            Add Photo
          </Text>
        </>
      )}
    </TouchableOpacity>
  );

  const gridData = [...photos, { id: 'add-button', secureUrl: '', isAddButton: true }];

  return (
    <View className="mb-6">
      <Text className={`mb-3 text-lg font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
        Profile Photos
      </Text>

      <FlatList
        data={gridData}
        renderItem={({ item }) => {
          if ('isAddButton' in item) {
            return renderAddButton();
          }
          return renderPhotoItem({ item: item as ProfilePhoto });
        }}
        keyExtractor={(item) => item.id}
        numColumns={3}
        columnWrapperStyle={{ justifyContent: 'space-between' }}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
      />

      {/* Photo View Modal */}
      <PhotoViewModal
        visible={isViewModalVisible}
        photo={selectedPhoto}
        onClose={() => setIsViewModalVisible(false)}
      />
    </View>
  );
}
