import { Ionicons, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { format } from 'date-fns';
import React, { useEffect, useRef, useState } from 'react';
import { Text, View, Image, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import LocationPreview from '../Map/LocationPreview';
import { RenderBackdrop } from '../RenderBackdrop';

import { useColorScheme } from '~/lib/useColorScheme';
import { useEvent } from '~/providers/MapProvider';
import { EventType } from '~/types';

// Ticket Info Component
const TicketsContent: React.FC<{
  event: EventType;
  onBack: () => void;
}> = ({ event, onBack }) => {
  const { colors } = useColorScheme();

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'EEEE, MMMM d');
  };

  const formatTime = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'h:mm a');
  };

  const getTicketInfo = () => {
    if (!event) return null;

    if (!event.isPaid) {
      return {
        isFree: true,
        price: 'Free',
        totalTickets: event.ticketSetup.totalTickets,
      };
    }

    if (event.ticketSetup.hasLevels) {
      return {
        isFree: false,
        hasLevels: true,
        levels: event.ticketSetup.levels,
      };
    }

    return {
      isFree: false,
      hasLevels: false,
      price: event.ticketSetup.price,
      totalTickets: event.ticketSetup.totalTickets,
    };
  };

  const ticketInfo = getTicketInfo() || { isFree: true, price: 0, totalTickets: 0 };

  return (
    <BottomSheetScrollView contentContainerStyle={{ paddingBottom: 20 }}>
      <View className="p-4">
        {/* Back Button */}
        <TouchableOpacity className="mb-4 flex-row items-center" onPress={onBack}>
          <MaterialIcons name="arrow-back" size={24} color={colors.foreground} />
          <Text className="ml-2 font-medium text-base " style={{ color: colors.foreground }}>
            Back to Event
          </Text>
        </TouchableOpacity>

        <View className="mb-6">
          <Text className="light:text-light-text mb-1 font-bold text-2xl dark:text-dark-text">
            {event?.title}
          </Text>
          <Text className="light:text-light-text/70 font-medium text-base dark:text-dark-text/70">
            {formatDate(event?.startDateTime)} • {formatTime(event?.startDateTime)} to{' '}
            {formatTime(event?.endDateTime)}
          </Text>
        </View>

        <View className="mb-6">
          <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
            Registration
          </Text>
          <Text className="light:text-light-text mb-4 text-base dark:text-dark-text">
            {ticketInfo?.isFree
              ? 'Hello! To join the event, please register below.'
              : 'Select your ticket type to continue with your purchase.'}
          </Text>

          {ticketInfo?.isFree ? (
            <TouchableOpacity className="rounded-lg bg-light-primary py-3 dark:bg-dark-primary">
              <Text className="text-center font-bold text-base text-white">Register</Text>
            </TouchableOpacity>
          ) : ticketInfo?.hasLevels ? (
            <View className="space-y-3">
              {ticketInfo.levels?.map((level: any, index: number) => (
                <View
                  key={index}
                  className="rounded-lg border border-gray-200 p-4 dark:border-gray-700">
                  <View className="mb-2 flex-row items-center justify-between">
                    <Text className="light:text-light-text font-bold text-base dark:text-dark-text">
                      {level.type}
                    </Text>
                    <Text className="light:text-light-primary font-bold text-base dark:text-dark-primary">
                      {event.currency} {level.price}
                    </Text>
                  </View>
                  <Text className="light:text-light-text/70 mb-3 text-sm dark:text-dark-text/70">
                    {level.quantity} tickets available
                  </Text>
                  <TouchableOpacity className="rounded-lg bg-light-primary py-2 dark:bg-dark-primary">
                    <Text className="text-center font-bold text-white">Select</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          ) : (
            <View className="mb-4 rounded-lg border border-gray-200 p-4 dark:border-gray-700">
              <View className="mb-2 flex-row items-center justify-between">
                <Text className="light:text-light-text font-bold text-base dark:text-dark-text">
                  Standard Ticket
                </Text>
                <Text className="light:text-light-primary font-bold text-base dark:text-dark-primary">
                  {event.currency} {ticketInfo.price}
                </Text>
              </View>
              <Text className="light:text-light-text/70 mb-3 text-sm dark:text-dark-text/70">
                {ticketInfo.totalTickets} tickets available
              </Text>
              <TouchableOpacity className="rounded-lg bg-light-primary py-2 dark:bg-dark-primary">
                <Text className="text-center font-bold text-white">Buy Ticket</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {!ticketInfo?.isFree && (
          <View className="mb-4 flex-row items-start gap-4">
            <MaterialCommunityIcons name="shield" size={24} color={colors.foreground} />
            <View className="flex-1">
              <Text className="light:text-light-text font-medium text-subtitle dark:text-dark-text">
                Buyer Guarantee Protected
              </Text>
              <Text className="font-regular light:text-light-text/70 text-body dark:text-dark-text/70">
                Every ticket is protected. If your event gets canceled, we'll make it right.
              </Text>
            </View>
          </View>
        )}
      </View>
    </BottomSheetScrollView>
  );
};

const EventMarkerSheetContent: React.FC = () => {
  const { selectedEvent, setSelectedEvent } = useEvent();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const { colors } = useColorScheme();
  const [showTickets, setShowTickets] = useState(false);
  const insets = useSafeAreaInsets();

  useEffect(() => {
    if (selectedEvent) {
      bottomSheetRef.current?.expand();
    } else {
      bottomSheetRef.current?.close();
      setShowTickets(false);
    }
  }, [selectedEvent]);

  const handleSheetChanges = (index: number) => {
    if (index === -1) {
      setSelectedEvent(null);
      setShowTickets(false);
    }
  };

  const getEventImage = () => {
    // Use a default image if no cover image is provided
    if (!selectedEvent?.coverImage) {
      const eventTypeImages: Record<string, string> = {
        Business:
          'https://images.unsplash.com/photo-1565398305935-49e5dcc5c9f6?q=80&w=1920&auto=format',
        Leisure:
          'https://images.unsplash.com/photo-1517457373958-b7bdd4587205?q=80&w=1920&auto=format',
        Entertainment:
          'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=1920&auto=format',
        Educational:
          'https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=1920&auto=format',
      };
      const eventType = selectedEvent?.eventType || '';
      return (
        eventTypeImages[eventType] || 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4'
      );
    }
    return selectedEvent.coverImage;
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={selectedEvent ? 0 : -1}
      snapPoints={['100%']}
      enablePanDownToClose
      onClose={() => {
        setSelectedEvent(null);
        setShowTickets(false);
      }}
      onChange={handleSheetChanges}
      backgroundStyle={{ backgroundColor: colors.background }}
      enableOverDrag={false}
      backdropComponent={RenderBackdrop}
      containerStyle={{
        zIndex: 100,
        elevation: 100,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
      handleIndicatorStyle={{
        backgroundColor: colors.foreground,
      }}
      topInset={insets.top}
      bottomInset={0}>
      {showTickets && selectedEvent ? (
        <TicketsContent event={selectedEvent} onBack={() => setShowTickets(false)} />
      ) : (
        <BottomSheetScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}>
          <View
            className="flex-row items-center justify-between border-b px-4 py-4"
            style={{ borderBottomColor: colors.grey5 }}>
            <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
              Event Details
            </Text>
            <TouchableOpacity
              className="p-2"
              onPress={() => {
                setSelectedEvent(null);
                setShowTickets(false);
              }}>
              <Ionicons name="close" size={24} color={colors.foreground} />
            </TouchableOpacity>
          </View>

          {/* Event Header Image */}
          <View className="relative h-48 w-full">
            <Image
              source={{ uri: getEventImage() }}
              className="h-full w-full"
              style={{ resizeMode: 'cover' }}
            />

            <View className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
              <Text className="mb-1 font-bold text-2xl uppercase text-white">
                {selectedEvent?.title}
              </Text>
              <Text className="text-base text-white">{selectedEvent?.location}</Text>
            </View>
          </View>

          {/* Event Details */}
          <View className="p-4">
            <Text className="light:text-light-text mb-4 font-bold text-2xl dark:text-dark-text">
              {selectedEvent?.location} - {selectedEvent?.title}
            </Text>

            {/* Date and Time */}
            <View className="mb-6 flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="light:text-light-text font-medium text-lg dark:text-dark-text">
                  {selectedEvent?.startDateTime
                    ? format(new Date(selectedEvent.startDateTime), 'EEEE')
                    : ''}
                </Text>

                <Text className="light:text-light-text/70 text-base dark:text-dark-text/70">
                  {selectedEvent?.startDateTime
                    ? format(new Date(selectedEvent.startDateTime), 'MMMM d, h:mm a')
                    : ''}{' '}
                  to{' '}
                  {selectedEvent?.endDateTime
                    ? format(new Date(selectedEvent.endDateTime), 'h:mm a')
                    : ''}
                </Text>
                <Text className="light:text-light-text/70 mb-2 text-base dark:text-dark-text/70">
                  {selectedEvent?.locationData.address || selectedEvent?.location}
                </Text>
              </View>
              <View className="items-end"></View>
            </View>

            {/* Location */}
            <View className="mb-6">
              {selectedEvent?.locationData && (
                <LocationPreview
                  location={{
                    coordinates: selectedEvent.locationData.coordinates,
                    manualAddress: selectedEvent.locationData.address,
                    name: selectedEvent.locationData.name,
                    address: selectedEvent.locationData.address || '',
                  }}
                  editable={false}
                />
              )}
            </View>

            {/* Registration Section */}
            <View className="mb-6">
              <TouchableOpacity
                onPress={() => setShowTickets(true)}
                className="flex-row items-center justify-center rounded-full bg-light-primary px-4 py-3 dark:bg-dark-primary">
                <MaterialIcons
                  name="how-to-reg"
                  size={20}
                  color="white"
                  style={{ marginRight: 8 }}
                />
                <Text className="text-center font-bold text-base text-white">
                  {selectedEvent?.isPaid ? 'View Tickets' : 'Register'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* About Event Section */}
            <View className="mb-6">
              <Text className="light:text-light-text mb-2 font-bold text-lg dark:text-dark-text">
                About Event
              </Text>
              <Text className="light:text-light-text text-base dark:text-dark-text">
                {selectedEvent?.description}
              </Text>
            </View>

            {/* Hosts Section */}
            <View className="mb-6">
              <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
                Hosts
              </Text>
              {selectedEvent?.owners.map((owner, index) => (
                <View key={index} className="mb-2 flex-row items-center">
                  <Image
                    source={{ uri: 'https://i.pravatar.cc/150?img=' + (index + 1) }}
                    className="mr-3 h-10 w-10 rounded-full"
                    style={{ resizeMode: 'cover' }}
                  />
                  <View>
                    <Text className="light:text-light-text font-medium text-base dark:text-dark-text">
                      {owner}
                    </Text>
                    <View className="mt-1 flex-row items-center">
                      {/* Display 5 stars with random rating for demo purposes */}
                      {[1, 2, 3, 4, 5].map((star) => {
                        // Generate a random rating between 3-5 for demo
                        const rating = 3 + Math.floor(Math.random() * 2.1);
                        return (
                          <Ionicons
                            key={star}
                            name={star <= rating ? 'star' : 'star-outline'}
                            size={14}
                            color="#FFD700"
                            style={{ marginRight: 2 }}
                          />
                        );
                      })}
                      <Text className="light:text-light-text/70 ml-1 text-xs dark:text-dark-text/70">
                        {(3 + Math.random() * 2).toFixed(1)}/5.0
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </View>
        </BottomSheetScrollView>
      )}
    </BottomSheet>
  );
};

export default function EventMarkerSheet() {
  return (
    <View
      style={{
        position: 'absolute',
        zIndex: 100,
        width: '100%',
        height: '100%',
        pointerEvents: 'box-none',
      }}>
      <EventMarkerSheetContent />
    </View>
  );
}
